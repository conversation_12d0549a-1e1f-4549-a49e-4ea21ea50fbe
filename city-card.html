<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>Upland Card – HTML/CSS</title>
<style>
/* ====== Design tokens ====== */
:root{
  --radius:16px;
  --pad:14px;
  --bg:#0c1630;         /* page bg */
  --text:#2a2312;       /* dark text on gold/silver/bronze */
  --icon:#2a2312;

  /* rank palette defaults (gold) */
  --c1:#ffde7a; /* light */
  --c2:#f0b73e; /* mid */
  --c3:#b37b14; /* dark rim */
  --c4:#fff3c6; /* highlight */
  --shadow:rgba(0,0,0,.35);
}

/* Silver & Bronze variants */
.card.rank-2{ --c1:#e6ebf3; --c2:#c8ced8; --c3:#8a919b; --c4:#ffffff; --text:#1e2430; --icon:#1e2430;}
.card.rank-3{ --c1:#e0b38b; --c2:#c48755; --c3:#7a4e2b; --c4:#fff0d8; --text:#2d1b10; --icon:#2d1b10;}

body{margin:0;min-height:100svh;display:grid;place-items:center;background:#061022 radial-gradient(1200px 600px at 50% 110%,#1a2a57 0,#061022 60%) no-repeat;font-family:ui-rounded,system-ui,Segoe UI,Roboto,Inter,sans-serif}

/* ====== Card ====== */
.card{
  position:relative;
  width:min(360px,92vw);
  color:var(--text);
  border-radius:calc(var(--radius) + 6px);
  padding:10px;                 /* border thickness */
  box-shadow:
    0 18px 30px -10px var(--shadow),
    0 2px 0 0 rgba(255,255,255,.15) inset;
  background:
    /* outer rim gradient */
    linear-gradient(145deg,var(--c3),var(--c2));
  isolation:isolate;
}

/* inner panel with bevel + subtle texture */
.card__panel{
  border-radius:var(--radius);
  background:
    /* top glossy strip */
    linear-gradient( to bottom, rgba(255,255,255,.16), transparent 22%),
    /* main metal */
    linear-gradient(180deg,var(--c1),var(--c2) 65%),
    /* faint noise fallback color */
    var(--c2);
  box-shadow:
    0 1px 0 0 rgba(255,255,255,.55) inset,
    0 -1px 0 0 rgba(0,0,0,.18) inset,
    0 0 0 1px rgba(0,0,0,.06) inset;
  overflow:hidden;
}

/* subtle grid texture for CITY */
.card.city .card__panel::after{
  content:"";
  position:absolute; inset:0;
  background:
    repeating-linear-gradient(90deg, rgba(0,0,0,.06), rgba(0,0,0,.06) 1px, transparent 1px, transparent 20px),
    repeating-linear-gradient( 0deg, rgba(0,0,0,.06), rgba(0,0,0,.06) 1px, transparent 1px, transparent 20px);
  mix-blend-mode:multiply; pointer-events:none;
  opacity:.25;
}

/* ---------- Ribbon (replaces .badge) ---------- */
:root{
  /* default ribbon colors (blue like ref) */
  --rb-light:#59b2e6;
  --rb-dark:#1e7fb6;
  --rb-shadow:rgba(0,0,0,.35);
}
/* make ribbon follow rank if you want */
.card.rank-1{ --rb-light:#ffd86a; --rb-dark:#c89623; }   /* gold */
.card.rank-2{ --rb-light:#e6ebf3; --rb-dark:#a9b1bd; }   /* silver */
.card.rank-3{ --rb-light:#e0b38b; --rb-dark:#a06b3e; }   /* bronze */

.ribbon{
  position:absolute; top:-18px; left:10px; z-index:4;
  height:40px; line-height:40px;
  padding:0 18px 0 24px; color:#0b0f14;
  font-weight:900; font-size:18px; letter-spacing:.02em;
  border-radius:12px;
  background:
    linear-gradient(180deg, rgba(255,255,255,.35), transparent 50%),
    linear-gradient(145deg, var(--rb-light), var(--rb-dark));
  box-shadow:
    0 10px 18px -8px var(--rb-shadow),
    0 1px 0 rgba(255,255,255,.6) inset,
    0 -2px 6px rgba(0,0,0,.18) inset;
}
/* left tail */
.ribbon::before{
  content:""; position:absolute; left:-18px; top:0;
  width:0; height:0;
  border-top:20px solid transparent;
  border-bottom:20px solid transparent;
  border-right:18px solid var(--rb-dark);
  filter:brightness(.9);
}
/* right tail (fold) */
.ribbon::after{
  content:""; position:absolute; right:-18px; top:8px;
  width:0; height:0;
  border-top:12px solid transparent;
  border-bottom:12px solid transparent;
  border-left:18px solid var(--rb-dark);
  filter:brightness(.85);
  border-radius:2px;
}
/* small curl highlight at the top edge */
.ribbon > span{
  display:inline-block; padding:0 2px;
  text-shadow:0 1px 0 rgba(255,255,255,.45);
}

/* ====== Header strip ====== */
.header{
  display:flex; align-items:center; gap:8px;
  padding:8px var(--pad) 6px;
  letter-spacing:.04em; font-weight:800;
  text-transform:uppercase; font-size:14px;
  background:
    linear-gradient(180deg, rgba(0,0,0,.10), rgba(0,0,0,0) 70%),
    linear-gradient(180deg, var(--c2), var(--c1));
  border-bottom:1px solid rgba(0,0,0,.12);
}

/* ====== Image window ====== */
.media{
  position:relative; margin:10px var(--pad) 0; border-radius:12px;
  aspect-ratio:16/9; overflow:hidden;
  box-shadow: inset 0 0 0 1px rgba(0,0,0,.15), inset 0 -30px 40px rgba(0,0,0,.08);
}
.media img{width:100%; height:100%; object-fit:cover; display:block;}

/* ====== Title ====== */
.title{
  padding:10px var(--pad) 0;
}
.title h3{margin:0; font-size:22px; line-height:1.1; font-weight:900;}
.title p{margin:2px 0 0; font-size:13px; opacity:.85;}

/* ====== Stats ====== */
.stats{
  display:grid; grid-template-columns:1fr 1fr 1fr; gap:8px;
  padding:10px var(--pad) var(--pad);
  background:linear-gradient(180deg, rgba(255,255,255,.08), rgba(0,0,0,.04));
  border-top:1px solid rgba(0,0,0,.1);
}
.stat{display:grid; grid-template-columns:auto 1fr; align-items:center; gap:8px; font-weight:800;}
.stat small{display:block; font-weight:700; font-size:11px; opacity:.85;}
.icon{width:18px; height:18px; color:var(--icon);}

/* nice hover */
.card:where(:hover){transform:translateY(-2px); transition:.2s ease; box-shadow:0 24px 36px -12px var(--shadow);}
</style>
</head>
<body>

<!-- Toggle rank with rank-1/rank-2/rank-3; toggle type with .city/.neighborhood -->
<article class="card rank-1 city" aria-label="Top City: Sarasota">
  <div class="ribbon"><span>1</span></div>

  <section class="card__panel">
    <div class="header">City</div>

    <figure class="media">
      <!-- Replace src with your city/hood art -->
      <img src="https://images.unsplash.com/photo-1505761671935-60b3a7427bad?q=80&w=1200&auto=format&fit=crop" alt="Sarasota skyline" />
    </figure>

    <div class="title">
      <h3>SARASOTA</h3>
      <p>Florida, USA</p>
    </div>

    <div class="stats" role="list">
      <div class="stat" role="listitem">
        <svg class="icon" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M16 11a4 4 0 1 0-4-4a4 4 0 0 0 4 4M8 12a3 3 0 1 0-3-3a3 3 0 0 0 3 3m0 2c-2.67 0-8 1.34-8 4v2h10v-2c0-1.38.56-2.63 1.5-3.67A10.63 10.63 0 0 0 8 14m8-2c-3.33 0-10 1.67-10 5v3h20v-3c0-3.33-6.67-5-10-5"/></svg>
        <div><small>Residents</small>4185</div>
      </div>
      <div class="stat" role="listitem">
        <svg class="icon" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M12 3l9 8h-3v8h-5v-5H11v5H6v-8H3z"/></svg>
        <div><small>Occupancy</small>94%</div>
      </div>
      <div class="stat" role="listitem">
        <svg class="icon" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M3 21V3h4v18H3m7 0V8h4v13h-4m7 0V13h4v8h-4"/></svg>
        <div><small>Density</small>94%</div>
      </div>
    </div>
  </section>
</article>

</body>
</html>
