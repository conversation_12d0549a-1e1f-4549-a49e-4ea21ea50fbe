/* ========= City Card Component ========= */
:root{
  --radius:12px;
  --pad:10px;
  --text:#2a2312;
  --icon:#2a2312;

  /* Золото (много слоёв для металла) */
  --gold-1:#fff6cf;  /* spec highlight */
  --gold-2:#ffe27a;
  --gold-3:#ffd049;
  --gold-4:#f6b53a;
  --gold-5:#b57919;  /* rim */
  --gold-6:#8b6f2a;  /* deep */
  
  /* Серебро */
  --silver-1:#ffffff;  /* spec highlight */
  --silver-2:#f0f4f8;
  --silver-3:#e6ebf3;
  --silver-4:#c8ced8;
  --silver-5:#a9b1bd;  /* rim */
  --silver-6:#8a919b;  /* deep */
  
  /* Бронза */
  --bronze-1:#fff0d8;  /* spec highlight */
  --bronze-2:#f4d4a7;
  --bronze-3:#e0b38b;
  --bronze-4:#c48755;
  --bronze-5:#a06b3e;  /* rim */
  --bronze-6:#7a4e2b;  /* deep */
  
  --shadow:rgba(0,0,0,.42);
}

.city-card{
  position: relative;
  width: 210px;
  aspect-ratio: 5/7;
  color: var(--text);
  border-radius: var(--radius);
  padding: 6px;
  box-shadow:
    0 9px 18px -5px var(--shadow),
    0 1px 0 rgba(255,255,255,.18) inset;
  background:
    linear-gradient(180deg, rgba(255,255,255,.35), rgba(255,255,255,0) 50%) padding-box,
    conic-gradient(from 210deg at 30% 10%, var(--gold-1), var(--gold-2), var(--gold-3), var(--gold-4), var(--gold-5), var(--gold-3)) border-box;
  isolation: isolate;
  transition: transform .25s ease, box-shadow .25s ease;
  font-family: ui-rounded, system-ui, Segoe UI, Inter, Roboto, sans-serif;
}

/* Hover эффект убран */

.city-card__panel{
  height: 100%;
  border-radius: calc(var(--radius) - 4px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background:
    linear-gradient(to bottom, rgba(255,255,255,.45), transparent 26%),
    linear-gradient(180deg, var(--gold-2), var(--gold-4) 70%, var(--gold-6) 100%);
  box-shadow:
    0 -1px 0 rgba(255,255,255,.45) inset,
    0 2px 5px rgba(0,0,0,.28) inset,
    0 0 0 0.5px rgba(0,0,0,.12) inset;
  position: relative;
}

.city-card__panel::after{
  content: "";
  position: absolute;
  inset: 0;
  background:
    repeating-linear-gradient(45deg,
      rgba(255,255,255,.06) 0 1px,
      transparent 1px 3px),
    repeating-linear-gradient(135deg,
      rgba(0,0,0,.03) 0 1.5px,
      transparent 1.5px 4.5px);
  mix-blend-mode: overlay;
  opacity: .55;
  pointer-events: none;
}

.city-card__title{
  text-align: center;
  padding: var(--pad) var(--pad) 6px;
  position: relative;
  z-index: 1;
}

.city-card__title h1{
  margin: 0;
  font-size: 18px;
  line-height: .9;
  font-weight: 900;
  letter-spacing: .02em;
  text-shadow:
    0 1px 0 rgba(255,255,255,.65),
    0 3px 6px rgba(0,0,0,.25);
}

.city-card__title p{
  margin: 3px 0 0;
  font-size: 8.5px;
  opacity: .9;
  font-weight: 700;
  text-shadow: 0 0.5px 0 rgba(255,255,255,.6);
}

.city-card__media{
  position: relative;
  margin: 0 var(--pad);
  border-radius: 10px;
  height: 57%;
  overflow: hidden;
  box-shadow:
    inset 0 0 0 1.5px rgba(0,0,0,.20),
    inset 0 -30px 35px rgba(0,0,0,.18),
    0 5px 9px rgba(0,0,0,.28);
}

.city-card__media img{
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.city-card__stats{
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
  padding: 12px var(--pad) calc(var(--pad) * 1.5);
  text-align: center;
  flex: 1.5;
  align-items: center;
  justify-items: center;
  justify-content: center;
  background: linear-gradient(180deg, rgba(255,255,255,.08), rgba(0,0,0,.06));
  border-top: 0.5px solid rgba(0,0,0,.16);
}

.city-card__stat{
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  font-weight: 800;
  position: relative;
}

.city-card__stat:not(:last-child)::after{
  content: "";
  position: absolute;
  right: -4.5px;
  top: 4px;
  bottom: 4px;
  width: 0.5px;
  background: linear-gradient(180deg, rgba(0,0,0,.18), rgba(255,255,255,.35));
  opacity: .45;
}

.city-card__stat-label{
  font-size: 10px;
  opacity: .85;
  font-weight: 700;
  line-height: 1;
}

.city-card__stat-value{
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 18px;
  font-weight: 900;
}

.city-card__icon{
  width: 16px;
  height: 16px;
  color: var(--icon);
}

/* Modifiers for positioning */
.city-card--overlay{
  position: fixed;
  top: 60px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

.city-card--overlay:hover{
  transform: translateX(-50%) translateY(-2px);
}

/* ========= Rank Variants ========= */

/* Silver Card */
.city-card--silver {
  background:
    linear-gradient(180deg, rgba(255,255,255,.35), rgba(255,255,255,0) 50%) padding-box,
    conic-gradient(from 210deg at 30% 10%, var(--silver-1), var(--silver-2), var(--silver-3), var(--silver-4), var(--silver-5), var(--silver-3)) border-box;
}

.city-card--silver .city-card__panel {
  background:
    linear-gradient(to bottom, rgba(255,255,255,.45), transparent 26%),
    linear-gradient(180deg, var(--silver-2), var(--silver-4) 70%, var(--silver-6) 100%);
}

.city-card--silver .city-card__title {
  color: #1e2430;
}

.city-card--silver .city-card__title h1,
.city-card--silver .city-card__title p {
  color: #1e2430;
}

.city-card--silver .city-card__icon {
  color: #1e2430;
}

.city-card--silver .city-card__stat-label,
.city-card--silver .city-card__stat-value {
  color: #1e2430;
}

/* Bronze Card */
.city-card--bronze {
  background:
    linear-gradient(180deg, rgba(255,255,255,.35), rgba(255,255,255,0) 50%) padding-box,
    conic-gradient(from 210deg at 30% 10%, var(--bronze-1), var(--bronze-2), var(--bronze-3), var(--bronze-4), var(--bronze-5), var(--bronze-3)) border-box;
}

.city-card--bronze .city-card__panel {
  background:
    linear-gradient(to bottom, rgba(255,255,255,.45), transparent 26%),
    linear-gradient(180deg, var(--bronze-2), var(--bronze-4) 70%, var(--bronze-6) 100%);
}

.city-card--bronze .city-card__title {
  color: #2d1b10;
}

.city-card--bronze .city-card__title h1,
.city-card--bronze .city-card__title p {
  color: #2d1b10;
}

.city-card--bronze .city-card__icon {
  color: #2d1b10;
}

.city-card--bronze .city-card__stat-label,
.city-card--bronze .city-card__stat-value {
  color: #2d1b10;
}