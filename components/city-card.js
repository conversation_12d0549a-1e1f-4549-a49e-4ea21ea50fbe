/**
 * City Card Component
 * Управляет созданием и обновлением карточек городов
 */
class CityCard {
  constructor(containerSelector) {
    this.container = document.querySelector(containerSelector);
    this.template = null;
    this.currentCard = null;
    this.cities = [];
    this.currentCityIndex = 0;
    
    this.init();
  }

  async init() {
    await this.loadTemplate();
    await this.loadCities();
    this.render();
  }

  async loadTemplate() {
    try {
      const response = await fetch('./components/city-card.html');
      const html = await response.text();
      
      // Создаем временный div для парсинга HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;
      
      // Получаем template
      this.template = tempDiv.querySelector('#city-card-template');
      
      if (!this.template) {
        throw new Error('Template not found');
      }
    } catch (error) {
      console.error('Error loading template:', error);
    }
  }

  async loadCities() {
    try {
      const response = await fetch('./data/cities.json');
      const data = await response.json();
      this.cities = data.cities;
    } catch (error) {
      console.error('Error loading cities data:', error);
      // Fallback data
      this.cities = [{
        id: "sarasota",
        name: "SARASOTA",
        location: "Florida, USA",
        image: "https://images.unsplash.com/photo-1505761671935-60b3a7427bad?q=80&w=1200&auto=format&fit=crop",
        residents: 4185,
        occupancy: "94%",
        density: "94%"
      }];
    }
  }

  render(cityIndex = 0) {
    if (!this.template || !this.cities.length) return;

    const city = this.cities[cityIndex];
    if (!city) return;

    // Клонируем template
    const cardElement = this.template.content.cloneNode(true);
    
    // Заполняем данными
    this.populateCard(cardElement, city);
    
    // Добавляем модификаторы для позиционирования
    const article = cardElement.querySelector('.city-card');
    article.classList.add('city-card--overlay');
    
    // Очищаем контейнер и добавляем новую карточку
    this.container.innerHTML = '';
    this.container.appendChild(cardElement);
    
    this.currentCard = this.container.querySelector('.city-card');
    this.currentCityIndex = cityIndex;
    
    // Добавляем обработчики событий
    this.addEventListeners();
  }

  populateCard(cardElement, city) {
    // Заполняем текстовые данные
    cardElement.querySelector('.city-card__name').textContent = city.name;
    cardElement.querySelector('.city-card__location').textContent = city.location;
    cardElement.querySelector('.city-card__residents').textContent = city.residents;
    cardElement.querySelector('.city-card__occupancy').textContent = city.occupancy;
    cardElement.querySelector('.city-card__density').textContent = city.density;
    
    // Заполняем изображение
    const img = cardElement.querySelector('.city-card__image');
    img.src = city.image;
    img.alt = `${city.name} skyline`;
    
    // Устанавливаем ID
    const article = cardElement.querySelector('.city-card');
    article.setAttribute('data-city-id', city.id);
    article.setAttribute('aria-label', `City: ${city.name}`);
  }

  addEventListeners() {
    if (!this.currentCard) return;

    // Клик по карточке для переключения города
    this.currentCard.addEventListener('click', () => {
      this.nextCity();
    });

    // Клавиатурная навигация
    document.addEventListener('keydown', (e) => {
      if (e.key === 'ArrowLeft') {
        this.previousCity();
      } else if (e.key === 'ArrowRight') {
        this.nextCity();
      }
    });
  }

  nextCity() {
    const nextIndex = (this.currentCityIndex + 1) % this.cities.length;
    this.render(nextIndex);
  }

  previousCity() {
    const prevIndex = this.currentCityIndex === 0 
      ? this.cities.length - 1 
      : this.currentCityIndex - 1;
    this.render(prevIndex);
  }

  // Публичные методы для внешнего управления
  showCity(cityId) {
    const index = this.cities.findIndex(city => city.id === cityId);
    if (index !== -1) {
      this.render(index);
    }
  }

  getCurrentCity() {
    return this.cities[this.currentCityIndex];
  }

  getAllCities() {
    return this.cities;
  }

  // Метод для обновления данных города
  updateCityData(cityId, newData) {
    const cityIndex = this.cities.findIndex(city => city.id === cityId);
    if (cityIndex !== -1) {
      this.cities[cityIndex] = { ...this.cities[cityIndex], ...newData };
      if (this.currentCityIndex === cityIndex) {
        this.render(cityIndex);
      }
    }
  }
}

// Экспорт для использования в других файлах
window.CityCard = CityCard;