/* ========= Top Cities Layout Component ========= */
.top-cities {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 1000;
}

.top-cities__container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 110px;
}

/* Серебряная карточка - слева, перекрывается золотой */
.top-cities__silver {
  position: relative;
  z-index: 2;
  pointer-events: auto;
  transform: scale(0.9);
  opacity: 0.95;
  margin-right: -15px; /* Перекрытие с золотой */
  margin-top: 20px;
}

/* Золотая карточка - по центру, поверх всех */
.top-cities__gold {
  position: relative;
  z-index: 3;
  pointer-events: auto;
  /* Золотая карточка остается в натуральном размере */
}

/* Бронзовая карточка - справа, перекрывается золотой */
.top-cities__bronze {
  position: relative;
  z-index: 1;
  pointer-events: auto;
  transform: scale(0.85);
  opacity: 0.9;
  margin-left: -15px; /* Перекрытие с золотой */
  margin-top: 35px;
}

/* Убираем эффекты hover - все карточки выглядят одинаково */

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
  .top-cities__container {
    /* Сохраняем горизонтальное расположение */
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 80px;
    transform: scale(0.7); /* Уменьшаем всю группу карточек */
    transform-origin: center top;
  }

  /* Серебряная карточка - сохраняем относительное позиционирование */
  .top-cities__silver {
    position: relative;
    z-index: 2;
    pointer-events: auto;
    transform: scale(0.9);
    opacity: 0.95;
    margin-right: -15px;
    margin-top: 20px;
  }

  /* Золотая карточка - сохраняем по центру */
  .top-cities__gold {
    position: relative;
    z-index: 3;
    pointer-events: auto;
    /* Остается в натуральном размере относительно группы */
  }

  /* Бронзовая карточка - сохраняем справа */
  .top-cities__bronze {
    position: relative;
    z-index: 1;
    pointer-events: auto;
    transform: scale(0.85);
    opacity: 0.9;
    margin-left: -15px;
    margin-top: 35px;
  }

  /* Hover эффекты убраны */
}