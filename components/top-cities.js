/**
 * Top Cities Component
 * Управляет отображением топ-3 городов с разными рангами
 */
class TopCities {
  constructor(containerSelector) {
    this.container = document.querySelector(containerSelector);
    this.cardTemplate = null;
    this.cities = [];
    this.cardComponents = [];
    
    this.init();
  }

  async init() {
    await this.loadTemplate();
    await this.loadCities();
    this.render();
  }

  async loadTemplate() {
    try {
      const response = await fetch('./components/city-card.html');
      const html = await response.text();
      
      // Создаем временный div для парсинга HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;
      
      // Получаем template
      this.cardTemplate = tempDiv.querySelector('#city-card-template');
      
      if (!this.cardTemplate) {
        throw new Error('City card template not found');
      }
    } catch (error) {
      console.error('Error loading city card template:', error);
    }
  }

  async loadCities() {
    try {
      const response = await fetch('./data/cities.json');
      const data = await response.json();
      this.cities = data.topCities || [];
      
      // Сортируем по рангу
      this.cities.sort((a, b) => a.rank - b.rank);
      
    } catch (error) {
      console.error('Error loading cities data:', error);
      // Fallback data
      this.cities = [
        {
          id: "sarasota",
          name: "SARASOTA",
          location: "Florida, USA", 
          image: "https://images.unsplash.com/photo-1505761671935-60b3a7427bad?q=80&w=1200&auto=format&fit=crop",
          residents: 4185,
          occupancy: "94%",
          density: "94%",
          rank: 1,
          rankClass: "gold"
        },
        {
          id: "miami",
          name: "MIAMI",
          location: "Florida, USA",
          image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?q=80&w=1200&auto=format&fit=crop",
          residents: 8420,
          occupancy: "87%", 
          density: "92%",
          rank: 2,
          rankClass: "silver"
        },
        {
          id: "new-york",
          name: "NEW YORK",
          location: "New York, USA",
          image: "https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9?q=80&w=1200&auto=format&fit=crop",
          residents: 12500,
          occupancy: "96%",
          density: "98%",
          rank: 3,
          rankClass: "bronze"
        }
      ];
    }
  }

  render() {
    if (!this.cardTemplate || !this.cities.length) return;

    // Создаем основной контейнер
    const topCitiesElement = document.createElement('div');
    topCitiesElement.className = 'top-cities';
    
    const containerElement = document.createElement('div');
    containerElement.className = 'top-cities__container';
    
    // Создаем карточки в правильном порядке: серебряная, золотая, бронзовая
    const sortedCities = [...this.cities].sort((a, b) => {
      const order = { silver: 1, gold: 2, bronze: 3 };
      return order[a.rankClass] - order[b.rankClass];
    });
    
    sortedCities.forEach((city, index) => {
      const cardWrapper = this.createCityCard(city, index);
      containerElement.appendChild(cardWrapper);
    });
    
    topCitiesElement.appendChild(containerElement);
    
    // Очищаем контейнер и добавляем новый элемент
    this.container.innerHTML = '';
    this.container.appendChild(topCitiesElement);
    
    // Добавляем обработчики событий
    this.addEventListeners();
  }

  createCityCard(city, index) {
    // Клонируем template
    const cardElement = this.cardTemplate.content.cloneNode(true);
    
    // Заполняем данными
    this.populateCard(cardElement, city);
    
    // Добавляем классы для ранга и позиционирования
    const article = cardElement.querySelector('.city-card');
    
    // Добавляем класс ранга
    if (city.rankClass) {
      article.classList.add(`city-card--${city.rankClass}`);
    }
    
    // Создаем wrapper с позиционированием
    const wrapper = document.createElement('div');
    
    switch(city.rank) {
      case 1: // Gold - центр
        wrapper.className = 'top-cities__gold';
        break;
      case 2: // Silver - слева ниже
        wrapper.className = 'top-cities__silver';
        break;
      case 3: // Bronze - справа еще ниже
        wrapper.className = 'top-cities__bronze';
        break;
      default:
        wrapper.className = 'top-cities__other';
    }
    
    wrapper.appendChild(cardElement);
    return wrapper;
  }

  populateCard(cardElement, city) {
    // Заполняем текстовые данные
    cardElement.querySelector('.city-card__name').textContent = city.name;
    cardElement.querySelector('.city-card__location').textContent = city.location;
    cardElement.querySelector('.city-card__residents').textContent = city.residents;
    cardElement.querySelector('.city-card__occupancy').textContent = city.occupancy;
    cardElement.querySelector('.city-card__density').textContent = city.density;
    
    // Заполняем изображение
    const img = cardElement.querySelector('.city-card__image');
    img.src = city.image;
    img.alt = `${city.name} skyline`;
    
    // Устанавливаем ID и aria-label
    const article = cardElement.querySelector('.city-card');
    article.setAttribute('data-city-id', city.id);
    article.setAttribute('data-rank', city.rank);
    article.setAttribute('aria-label', `City: ${city.name}, Rank ${city.rank}`);
  }

  addEventListeners() {
    // Клики по карточкам
    const cards = this.container.querySelectorAll('.city-card');
    cards.forEach(card => {
      card.addEventListener('click', (e) => {
        const cityId = card.getAttribute('data-city-id');
        const rank = card.getAttribute('data-rank');
        
        console.log(`Clicked city: ${cityId}, rank: ${rank}`);
        
        // Можно добавить логику для взаимодействия с глобусом
        this.onCityClick(cityId, rank);
      });
    });

    // Клавиатурная навигация
    document.addEventListener('keydown', (e) => {
      if (e.key >= '1' && e.key <= '3') {
        const rank = parseInt(e.key);
        this.highlightCityByRank(rank);
      }
    });
  }

  onCityClick(cityId, rank) {
    // Эмитим кастомное событие для взаимодействия с другими компонентами
    const event = new CustomEvent('citySelected', {
      detail: { cityId, rank },
      bubbles: true
    });
    
    this.container.dispatchEvent(event);
    
    // Добавляем визуальную реакцию
    this.highlightCityByRank(parseInt(rank));
  }

  highlightCityByRank(rank) {
    // Убираем предыдущие выделения
    const cards = this.container.querySelectorAll('.city-card');
    cards.forEach(card => {
      card.classList.remove('city-card--highlighted');
    });
    
    // Добавляем выделение к выбранной карточке
    const targetCard = this.container.querySelector(`[data-rank="${rank}"]`);
    if (targetCard) {
      targetCard.classList.add('city-card--highlighted');
      
      // Убираем выделение через 2 секунды
      setTimeout(() => {
        targetCard.classList.remove('city-card--highlighted');
      }, 2000);
    }
  }

  // Публичные методы
  getCities() {
    return this.cities;
  }

  getCityByRank(rank) {
    return this.cities.find(city => city.rank === rank);
  }

  updateCityData(cityId, newData) {
    const cityIndex = this.cities.findIndex(city => city.id === cityId);
    if (cityIndex !== -1) {
      this.cities[cityIndex] = { ...this.cities[cityIndex], ...newData };
      this.render(); // Перерендериваем
    }
  }
}

// Добавляем стиль для выделения
const style = document.createElement('style');
style.textContent = `
  .city-card--highlighted {
    animation: highlight 0.5s ease-in-out;
  }
  
  @keyframes highlight {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }
`;
document.head.appendChild(style);

// Экспорт для использования в других файлах
window.TopCities = TopCities;