<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <title>Upland Globe</title>
  <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
  <link href="https://api.mapbox.com/mapbox-gl-js/v3.14.0/mapbox-gl.css" rel="stylesheet" />
  <!-- Import sexy Google Fonts for neighborhood labels -->
  <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&family=Orbitron:wght@400;500;700;900&display=swap" rel="stylesheet">
  <!-- City Card Component Styles -->
  <link href="./components/city-card.css" rel="stylesheet" />
  <!-- Top Cities Component Styles -->
  <link href="./components/top-cities.css" rel="stylesheet" />
  <script src="https://api.mapbox.com/mapbox-gl-js/v3.14.0/mapbox-gl.js"></script>
  <style>
    html,body,#map { height:100%; margin:0; font-family: ui-rounded, system-ui, Segoe UI, Inter, Roboto, sans-serif; }
  </style>
</head>
<body>
<div id="map"></div>

<!-- Top Cities Container -->
<div id="top-cities-container"></div>

<!-- Component Scripts -->
<script src="./components/city-card.js"></script>
<script src="./components/top-cities.js"></script>

<script>
  // Initialize Mapbox with access token
  mapboxgl.accessToken = 'pk.eyJ1IjoidXBsYW5kbWUiLCJhIjoiY2pydGF1OWttMHF2azN6bTl0MWRxNXp0cyJ9.b8JmL-_Beo4J3qZoB97wOw';

  // Create the globe map
  const map = new mapboxgl.Map({
    container: 'map',
    style: 'mapbox://styles/uplandme/cmet29kke005401s2hy4b5lv9',
    center: [-90, 20],
    zoom: 1.6,
    projection: 'globe'   // Enable globe projection
  });


  // When map loads, add atmospheric effects
  map.on('style.load', () => {
    map.setFog({}); // Add atmospheric effects
  });

  // Globe view settings
  const globeView = {
    center: [-90, 20],
    zoom: 1.6
  };



  // Spin logic
  const secondsPerRevolution = 120;
  const maxSpinZoom = 5;
  const slowSpinZoom = 3;
  let userInteracting = false;
  let spinEnabled = true;

  function spinGlobe() {
    if (!spinEnabled || userInteracting) return;
    const z = map.getZoom();
    if (z >= maxSpinZoom) return;
    let dps = 360 / secondsPerRevolution;
    if (z > slowSpinZoom) dps *= (maxSpinZoom - z) / (maxSpinZoom - slowSpinZoom);
    const c = map.getCenter();
    c.lng -= dps;
    map.easeTo({ center: c, duration: 1000, easing: n => n });
  }

  ['mousedown','dragstart','wheel','touchstart','rotatestart','pitchstart'].forEach(ev =>
    map.on(ev, () => { userInteracting = true; })
  );
  ['mouseup','dragend','rotateend','pitchend','touchend'].forEach(ev =>
    map.on(ev, () => { userInteracting = false; spinGlobe(); })
  );
  map.on('moveend', spinGlobe);

  spinGlobe();

  // Initialize Top Cities Component
  let topCities;
  
  // Wait for DOM to be ready
  document.addEventListener('DOMContentLoaded', () => {
    topCities = new TopCities('#top-cities-container');
    
    // Listen for city selection events
    document.addEventListener('citySelected', (e) => {
      const { cityId, rank } = e.detail;
      console.log(`City selected: ${cityId}, rank: ${rank}`);
      
      // Here you could add logic to interact with the globe
      // For example, fly to the city location
    });
  });

  // Optional: Add interaction between globe and city cards
  map.on('click', (e) => {
    console.log('Globe clicked at:', e.lngLat);
    // Could determine which city region was clicked and highlight corresponding card
  });

  // Keyboard shortcuts for cities (1, 2, 3)
  document.addEventListener('keydown', (e) => {
    if (e.key >= '1' && e.key <= '3' && topCities) {
      const rank = parseInt(e.key);
      console.log(`Keyboard shortcut for rank ${rank}`);
    }
  });

</script>
</body>
</html>
