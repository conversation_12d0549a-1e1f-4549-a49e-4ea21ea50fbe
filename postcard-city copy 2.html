<!doctype html>
<html lang="ru">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>Upland – Top Cities Podium</title>
<style>
:root{
  --radius:24px;
  --pad:20px;
  --bg:#0b1327;
  --shadow:rgba(0,0,0,.44);

  /* дефолтные тексты/иконки (перезапишем в серебре/бронзе) */
  --text:#2a2312;
  --icon:#2a2312;

  /* базовая палитра (по умолчанию золото) */
  --g1:#fff8d1; --g2:#ffe07a; --g3:#ffd24c; --g4:#f6b93b; --g5:#c4872c; --g6:#8b6f2a;
}

*{box-sizing:border-box}
body{
  margin:0; padding:32px 16px;
  min-height:100svh;
  background:
    radial-gradient(900px 480px at 50% 110%, #2a4070 0%, var(--bg) 60%) no-repeat,
    var(--bg);
  font-family:ui-rounded,system-ui,Inter,Segoe UI,Roboto,sans-serif;
  color:#fff;
}

/* ===== PODIUM LAYOUT ===== */
.podium{
  max-width:1200px; margin:0 auto;
}
.podium__title{
  font-size:28px; font-weight:900; letter-spacing:.02em; margin:0 0 18px 6px;
}
.podium__wrap{
  display:grid;
  grid-template-columns:1fr 1fr 1fr;
  grid-template-areas:
    "second first third"
    "second first third";
  gap:28px;
  align-items:end;
}
.podium__wrap .postcard{ transition:transform .25s ease, box-shadow .25s ease; }
.podium__wrap .postcard.rank-1{ grid-area:first; transform:translateY(-18px) scale(1.04); z-index:3; }
.podium__wrap .postcard.rank-2{ grid-area:second; transform:translateY(16px) scale(0.98); z-index:2; }
.podium__wrap .postcard.rank-3{ grid-area:third; transform:translateY(28px) scale(0.96); z-index:1; }

@media (max-width:1024px){
  .podium__wrap{
    grid-template-columns:1fr 1fr;
    grid-template-areas:
      "first first"
      "second third";
  }
  .podium__wrap .postcard{ transform:none; }
}
@media (max-width:640px){
  .podium__wrap{
    grid-template-columns:1fr;
    grid-template-areas:"first" "second" "third";
    gap:22px;
  }
}

/* ===== POSTCARD (ранги через CSS-переменные) ===== */
.postcard{
  position:relative;
  width:min(420px,95vw);
  aspect-ratio:5/7;
  border-radius:var(--radius);
  padding:12px;
  color:var(--text);
  overflow:hidden;
  box-shadow:
    0 22px 48px -14px var(--shadow),
    0 2px 0 rgba(255,255,255,.2) inset;
  background:
    linear-gradient(180deg, rgba(255,255,255,.35), transparent 55%) padding-box,
    conic-gradient(from 210deg at 28% 10%,
      var(--g1), var(--g2), var(--g3), var(--g4), var(--g5), var(--g3), var(--g2)) border-box;
  isolation:isolate;
}
.postcard::before{
  content:""; position:absolute; inset:-40%;
  background: linear-gradient(120deg, transparent 44%, rgba(255,255,255,.35) 50%, transparent 56%);
  transform: translateX(-120%) rotate(8deg);
  pointer-events:none;
}
.postcard:hover::before{ animation:shine 900ms ease forwards; }
@keyframes shine{ to{ transform: translateX(120%) rotate(8deg);} }

.panel{
  height:100%;
  border-radius:calc(var(--radius) - 8px);
  display:flex; flex-direction:column; overflow:hidden;
  background:
    linear-gradient(180deg, rgba(255,255,255,.5), transparent 26%),
    linear-gradient(180deg, var(--g2), var(--g4) 70%, var(--g6));
  box-shadow:
    inset 0 4px 12px rgba(255,255,255,.45),
    inset 0 -8px 16px rgba(0,0,0,.45),
    inset 0 0 0 1px rgba(0,0,0,.2);
  position:relative;
}
.panel::after{
  content:""; position:absolute; inset:0; pointer-events:none;
  background:
    url("https://grainy-gradients.vercel.app/noise.svg"),
    repeating-linear-gradient(45deg, rgba(255,255,255,.06) 0 2px, transparent 2px 7px),
    repeating-linear-gradient(135deg, rgba(0,0,0,.04) 0 3px, transparent 3px 10px);
  mix-blend-mode:overlay; opacity:.40;
  border-radius:calc(var(--radius) - 8px);
}

/* ===== Title ===== */
.title{ text-align:center; padding:var(--pad) var(--pad) 12px; z-index:1; }
.title h1{
  margin:0; font-size:36px; line-height:.9; font-weight:900; letter-spacing:.02em;
  text-shadow:0 1px 0 rgba(255,255,255,.85), 0 6px 12px rgba(0,0,0,.35);
}
.title p{
  margin:6px 0 0; font-size:17px; font-weight:700; opacity:.92;
  text-shadow:0 1px 0 rgba(255,255,255,.7);
}

/* ===== Image ===== */
.media{
  position:relative; margin:0 var(--pad); height:68%;
  border-radius:20px; overflow:hidden;
  box-shadow:
    inset 0 0 0 3px rgba(0,0,0,.22),
    inset 0 -70px 90px rgba(0,0,0,.22),
    0 12px 22px rgba(0,0,0,.30);
}
.media img{ width:100%; height:100%; object-fit:cover; display:block; }
.media::after{
  content:""; position:absolute; left:0; right:0; bottom:0; height:30%;
  background: linear-gradient(0deg, rgba(255,255,255,.06), transparent 70%); pointer-events:none;
}

/* ===== Stats ===== */
.stats{
  display:grid; grid-template-columns:1fr 1fr 1fr; gap:18px;
  padding:16px var(--pad) calc(var(--pad) * 1.08);
  text-align:center; align-items:center; justify-items:center;
  background: linear-gradient(180deg, rgba(255,255,255,.10), rgba(0,0,0,.08));
  border-top:1px solid rgba(0,0,0,.22);
}
.stat{ display:flex; flex-direction:column; align-items:center; gap:6px; font-weight:800; position:relative; }
.stat:not(:last-child)::after{
  content:""; position:absolute; right:-9px; top:8px; bottom:8px; width:1px;
  background:linear-gradient(180deg, rgba(0,0,0,.28), rgba(255,255,255,.45)); opacity:.45;
}
.stat .label{ font-size:14px; opacity:.88; font-weight:700; line-height:1; }
.stat .value{ display:flex; align-items:center; gap:10px; font-size:26px; font-weight:900; }
.icon{ width:28px; height:28px; color:var(--icon); }

/* ===== Ranks ===== */
.rank-1{ /* Gold (по умолчанию переменные уже золото) */ }
.rank-2{ /* Silver */
  --g1:#f2f6ff; --g2:#e4ebf7; --g3:#d6dce5; --g4:#bcc3cc; --g5:#969ba3; --g6:#5a5f66;
  --text:#1f232a; --icon:#1f232a;
}
.rank-3{ /* Bronze */
  --g1:#ffe5d1; --g2:#f7c99b; --g3:#e6a970; --g4:#c78952; --g5:#8a4d2c; --g6:#5a2c18;
  --text:#2d1a0f; --icon:#2d1a0f;
}
</style>
</head>
<body>

<section class="podium" aria-label="Top Cities">
  <h2 class="podium__title">UPLAND TOP CITIES</h2>

  <div class="podium__wrap">
    <!-- #2 -->
    <article class="postcard rank-2" aria-label="2nd place – San Francisco">
      <div class="panel">
        <div class="title">
          <h1>SAN FRANCISCO</h1>
          <p>California, USA</p>
        </div>
        <figure class="media">
          <img src="https://images.unsplash.com/photo-1501594907352-04cda38ebc29?q=80&w=1200&auto=format&fit=crop" alt="San Francisco skyline">
        </figure>
        <div class="stats">
          <div class="stat"><div class="label">Residents</div><div class="value">👥 3200</div></div>
          <div class="stat"><div class="label">Occupancy</div><div class="value">🏠 91%</div></div>
          <div class="stat"><div class="label">Density</div><div class="value">🏙️ 88%</div></div>
        </div>
      </div>
    </article>

    <!-- #1 -->
    <article class="postcard rank-1" aria-label="1st place – Sarasota">
      <div class="panel">
        <div class="title">
          <h1>SARASOTA</h1>
          <p>Florida, USA</p>
        </div>
        <figure class="media">
          <img src="https://images.unsplash.com/photo-1505761671935-60b3a7427bad?q=80&w=1200&auto=format&fit=crop" alt="Sarasota skyline">
        </figure>
        <div class="stats">
          <div class="stat"><div class="label">Residents</div><div class="value">👥 4185</div></div>
          <div class="stat"><div class="label">Occupancy</div><div class="value">🏠 94%</div></div>
          <div class="stat"><div class="label">Density</div><div class="value">🏙️ 94%</div></div>
        </div>
      </div>
    </article>

    <!-- #3 -->
    <article class="postcard rank-3" aria-label="3rd place – Mykolaiv">
      <div class="panel">
        <div class="title">
          <h1>MYKOLAIV</h1>
          <p>Ukraine</p>
        </div>
        <figure class="media">
          <img src="https://images.unsplash.com/photo-1544197150-b99a580bb7a8?q=80&w=1200&auto=format&fit=crop" alt="Mykolaiv city">
        </figure>
        <div class="stats">
          <div class="stat"><div class="label">Residents</div><div class="value">👥 1800</div></div>
          <div class="stat"><div class="label">Occupancy</div><div class="value">🏠 85%</div></div>
          <div class="stat"><div class="label">Density</div><div class="value">🏙️ 77%</div></div>
        </div>
      </div>
    </article>
  </div>
</section>

</body>
</html>
