<!doctype html>
<html lang="ru">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>City Postcard – Gold Metal</title>
<style>
/* ========= Tokens ========= */
:root{
  --radius:24px;
  --pad:20px;
  --bg:#0f1b35;
  --text:#2a2312;
  --icon:#2a2312;

  /* Золото (много слоёв для металла) */
  --gold-1:#fff6cf;  /* spec highlight */
  --gold-2:#ffe27a;
  --gold-3:#ffd049;
  --gold-4:#f6b53a;
  --gold-5:#b57919;  /* rim */
  --gold-6:#8b6f2a;  /* deep */
  --shadow:rgba(0,0,0,.42);
}

@media (prefers-color-scheme: dark){
  :root{ --bg:#0b1327; }
}

*{box-sizing:border-box}
body{
  margin:0; min-height:100svh; display:grid; place-items:center;
  background:
    radial-gradient(900px 480px at 50% 110%, #2a4070 0%, var(--bg) 60%) no-repeat,
    var(--bg);
  font-family: ui-rounded, system-ui, Segoe UI, Inter, Roboto, sans-serif;
  padding:22px;
}

/* ========= Card ========= */
.postcard{
  position:relative;
  width:min(420px,95vw);
  aspect-ratio:5/7;
  color:var(--text);
  border-radius:var(--radius);
  padding:12px; /* толщина рамки */
  /* объём + падающая тень */
  box-shadow:
    0 18px 36px -10px var(--shadow),
    0 2px 0 rgba(255,255,255,.18) inset;

  /* Рамка: линейный + конический градиенты создают металлический перелив */
  background:
    /* мягкий верхний блик на рамке */
    linear-gradient(180deg, rgba(255,255,255,.35), rgba(255,255,255,0) 50%) padding-box,
    /* основной золотой перелив */
    conic-gradient(from 210deg at 30% 10%, var(--gold-1), var(--gold-2), var(--gold-3), var(--gold-4), var(--gold-5), var(--gold-3)) border-box;
  isolation:isolate;
}

/* Внутренняя панель с «золотом» и тиснением */
.postcard__panel{
  height:100%;
  border-radius:calc(var(--radius) - 8px);
  overflow:hidden;
  display:flex; flex-direction:column;

  background:
    /* верхний specular highlight */
    linear-gradient(to bottom, rgba(255,255,255,.45), transparent 26%),
    /* основной градиент золота внутри */
    linear-gradient(180deg, var(--gold-2), var(--gold-4) 70%, var(--gold-6) 100%);

  box-shadow:
    /* внутренний блик по кромке */
    0 -2px 0 rgba(255,255,255,.45) inset,
    /* внутренняя тень по низу */
    0 4px 10px rgba(0,0,0,.28) inset,
    /* тонкий кант */
    0 0 0 1px rgba(0,0,0,.12) inset;
  position:relative;
}

/* Лёгкая «металлическая» текстура */
.postcard__panel::after{
  content:"";
  position:absolute; inset:0;
  background:
    repeating-linear-gradient(45deg,
      rgba(255,255,255,.06) 0 2px,
      transparent 2px 6px),
    repeating-linear-gradient(135deg,
      rgba(0,0,0,.03) 0 3px,
      transparent 3px 9px);
  mix-blend-mode:overlay;
  opacity:.55;
  pointer-events:none;
}

/* ========= Title ========= */
.title{ text-align:center; padding:var(--pad) var(--pad) 12px; position:relative; z-index:1; }
.title h1{
  margin:0; font-size:36px; line-height:.9; font-weight:900; letter-spacing:.02em;
  text-shadow:
    0 2px 0 rgba(255,255,255,.65),
    0 6px 12px rgba(0,0,0,.25);
}
.title p{
  margin:6px 0 0; font-size:17px; opacity:.9; font-weight:700;
  text-shadow:0 1px 0 rgba(255,255,255,.6);
}

/* ========= Image (увеличено, глубина) ========= */
.media{
  position:relative; margin:0 var(--pad);
  border-radius:20px; height:68%;
  overflow:hidden;
  box-shadow:
    inset 0 0 0 3px rgba(0,0,0,.20),
    inset 0 -60px 70px rgba(0,0,0,.18),
    0 10px 18px rgba(0,0,0,.28);
}
.media img{ width:100%; height:100%; object-fit:cover; display:block; }

/* ========= Stats ========= */
.stats{
  display:grid; grid-template-columns:1fr 1fr 1fr; gap:18px;
  padding:16px var(--pad) calc(var(--pad) * 1.05);
  text-align:center; align-items:center; justify-items:center;
  background: linear-gradient(180deg, rgba(255,255,255,.08), rgba(0,0,0,.06));
  border-top:1px solid rgba(0,0,0,.16);
}
.stat{ display:flex; flex-direction:column; align-items:center; gap:6px; font-weight:800; position:relative; }
.stat:not(:last-child)::after{
  content:""; position:absolute; right:-9px; top:8px; bottom:8px;
  width:1px; background:linear-gradient(180deg, rgba(0,0,0,.18), rgba(255,255,255,.35));
  opacity:.45;
}
.stat .label{ font-size:14px; opacity:.85; font-weight:700; line-height:1; }
.stat .value{ display:flex; align-items:center; gap:10px; font-size:26px; font-weight:900; }
.icon{ width:28px; height:28px; color:var(--icon); }

/* Hover */
.postcard{ transition:transform .25s ease, box-shadow .25s ease; }
.postcard:hover{ transform:translateY(-4px); box-shadow:0 28px 48px -14px var(--shadow); }
</style>
</head>
<body>

<article class="postcard" aria-label="City: Sarasota">
  <div class="postcard__panel">
    <div class="title">
      <h1>SARASOTA</h1>
      <p>Florida, USA</p>
    </div>

    <figure class="media">
      <img src="https://images.unsplash.com/photo-1505761671935-60b3a7427bad?q=80&w=1200&auto=format&fit=crop" alt="Sarasota skyline" />
    </figure>

    <div class="stats" role="list">
      <div class="stat" role="listitem">
        <div class="label">Residents</div>
        <div class="value">
          <svg class="icon" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M16 11a4 4 0 1 0-4-4a4 4 0 0 0 4 4M8 12a3 3 0 1 0-3-3a3 3 0 0 0 3 3m0 2c-2.67 0-8 1.34-8 4v2h10v-2c0-1.38.56-2.63 1.5-3.67A10.63 10.63 0 0 0 8 14m8-2c-3.33 0-10 1.67-10 5v3h20v-3c0-3.33-6.67-5-10-5"/></svg>
          4185
        </div>
      </div>
      <div class="stat" role="listitem">
        <div class="label">Occupancy</div>
        <div class="value">
          <svg class="icon" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M12 3l9 8h-3v8h-5v-5H11v5H6v-8H3z"/></svg>
          94%
        </div>
      </div>
      <div class="stat" role="listitem">
        <div class="label">Density</div>
        <div class="value">
          <svg class="icon" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M3 21V3h4v18H3m7 0V8h4v13h-4m7 0V13h4v8h-4"/></svg>
          94%
        </div>
      </div>
    </div>
  </div>
</article>

</body>
</html>