<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>City Postcard – HTML/CSS</title>
<style>
/* ====== Design tokens ====== */
:root{
  --radius:24px;
  --pad:20px;
  --bg:#1a2847;         /* page bg */
  --text:#2a2312;       /* dark text on gold */
  --icon:#2a2312;
  
  /* enhanced gold metallic palette */
  --gold-light:#FFD766; 
  --gold-mid:#e6c965;
  --gold-dark:#2e2818;
  --gold-rim:#8b6f2a;
  --highlight:#fff8cc;
  --shadow:rgba(0,0,0,.4);
}

body{
  margin:0; 
  min-height:100svh; 
  display:grid; 
  place-items:center; 
  background:#0f1b35 radial-gradient(1200px 600px at 50% 110%,#2a4070 0,#0f1b35 60%) no-repeat;
  font-family:ui-rounded,system-ui,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,sans-serif;
  padding:20px;
}

/* ====== Postcard ====== */
.postcard{
  position:relative;
  width:min(400px,95vw);
  aspect-ratio:5/7;
  color:var(--text);
  border-radius:var(--radius);
  padding:12px;                 /* frame thickness */
  box-shadow:
    0 6px 20px rgba(0,0,0,0.4),
    inset 0 -4px 8px rgba(255,255,255,0.5),
    inset 0 4px 12px rgba(0,0,0,0.4),
    inset 0 0 0 2px rgba(0,0,0,0.2);
  background:
    /* multi-layered gradient border */
    linear-gradient(145deg, #FFD766, #B98800);
  isolation:isolate;
}

/* inner panel with enhanced layered gradients and inset shadows */
.postcard__panel{
  height:100%;
  border-radius:calc(var(--radius) - 8px);
  background:
    /* bright highlight at top */
    linear-gradient(to bottom, rgba(255,255,255,.4), transparent 30%),
    /* stronger gold gradient light to dark */
    linear-gradient(180deg, #FFD766, #B98800 85%),
    /* fallback */
    var(--gold-mid);
  box-shadow:
    0 -2px 0 0 rgba(255,255,255,.3) inset,
    0 2px 0 0 rgba(0,0,0,.2) inset,
    0 0 0 1px rgba(0,0,0,.1) inset;
  overflow:hidden;
  display:flex;
  flex-direction:column;
}

/* ====== Title ====== */
.title{
  text-align:center;
  padding:var(--pad) var(--pad) 12px;
}
.title h1{
  margin:0; 
  font-size:36px; 
  line-height:.9; 
  font-weight:900;
  letter-spacing:.02em;
  text-shadow:
    0 2px 0 rgba(255,255,255,.6),
    0 4px 8px rgba(0,0,0,.2);
}
.title p{
  margin:4px 0 0; 
  font-size:17px; 
  opacity:.9;
  font-weight:700;
  text-shadow:0 1px 0 rgba(255,255,255,.5);
}

/* ====== Image window (70% height) ====== */
.media{
  position:relative; 
  margin:0 var(--pad);
  border-radius:20px;
  height:70%;
  overflow:hidden;
  box-shadow: 
    inset 0 0 0 3px rgba(0,0,0,.2), 
    inset 0 -50px 60px rgba(0,0,0,.15),
    0 8px 16px rgba(0,0,0,.25);
}
.media img{
  width:100%; 
  height:100%; 
  object-fit:cover; 
  display:block;
}

/* ====== Stats (20% of card height) ====== */
.stats{
  display:grid; 
  grid-template-columns:1fr 1fr 1fr; 
  gap:18px;
  padding:18px var(--pad) calc(var(--pad) * 1.1);
  text-align:center;
  flex:0 0 20%;
  align-items:center;
  justify-items:center;
}
.stat{
  display:flex;
  flex-direction:column;
  align-items:center;
  gap:6px;
  font-weight:800;
}
.stat .label{
  font-size:14px; 
  opacity:.85;
  font-weight:700;
  line-height:1;
}
.stat .value{
  display:flex;
  align-items:center;
  gap:10px;
  font-size:28px;
  font-weight:900;
}
.icon{
  width:32px; 
  height:32px; 
  color:var(--icon);
}

/* subtle hover effect */
.postcard:hover{
  transform:translateY(-3px); 
  transition:.3s ease; 
  box-shadow:0 26px 46px -14px var(--shadow);
}
</style>
</head>
<body>

<article class="postcard" aria-label="City: Sarasota">
  <div class="postcard__panel">
    <div class="title">
      <h1>SARASOTA</h1>
      <p>Florida, USA</p>
    </div>

    <figure class="media">
      <!-- Replace src with your city art -->
      <img src="https://images.unsplash.com/photo-1505761671935-60b3a7427bad?q=80&w=1200&auto=format&fit=crop" alt="Sarasota skyline" />
    </figure>

    <div class="stats" role="list">
      <div class="stat" role="listitem">
        <div class="label">Residents</div>
        <div class="value">
          <svg class="icon" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M16 11a4 4 0 1 0-4-4a4 4 0 0 0 4 4M8 12a3 3 0 1 0-3-3a3 3 0 0 0 3 3m0 2c-2.67 0-8 1.34-8 4v2h10v-2c0-1.38.56-2.63 1.5-3.67A10.63 10.63 0 0 0 8 14m8-2c-3.33 0-10 1.67-10 5v3h20v-3c0-3.33-6.67-5-10-5"/></svg>
          4185
        </div>
      </div>
      <div class="stat" role="listitem">
        <div class="label">Occupancy</div>
        <div class="value">
          <svg class="icon" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M12 3l9 8h-3v8h-5v-5H11v5H6v-8H3z"/></svg>
          94%
        </div>
      </div>
      <div class="stat" role="listitem">
        <div class="label">Density</div>
        <div class="value">
          <svg class="icon" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M3 21V3h4v18H3m7 0V8h4v13h-4m7 0V13h4v8h-4"/></svg>
          94%
        </div>
      </div>
    </div>
  </div>
</article>

</body>
</html>