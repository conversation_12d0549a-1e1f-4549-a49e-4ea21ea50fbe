<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upland Top Cities</title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&family=Orbitron:wght@400;500;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: radial-gradient(ellipse at center, #1e3c72 0%, #2a5298 25%, #1e3c72 50%, #0f2b5b 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            position: relative;
            overflow-x: hidden;
        }

        /* Globe background effect */
        body::before {
            content: '';
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 800px;
            height: 800px;
            background: radial-gradient(circle, rgba(70, 130, 180, 0.1) 0%, rgba(25, 25, 112, 0.05) 40%, transparent 70%);
            border-radius: 50%;
            z-index: 1;
            animation: pulse 4s ease-in-out infinite alternate;
        }

        @keyframes pulse {
            0% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
            100% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.6; }
        }

        .container {
            position: relative;
            z-index: 10;
            max-width: 1200px;
            width: 100%;
        }

        .title {
            font-family: 'Orbitron', monospace;
            font-size: 3.5rem;
            font-weight: 900;
            color: #FFD700;
            text-shadow: 0 0 30px rgba(255, 215, 0, 0.5), 0 0 60px rgba(255, 215, 0, 0.3);
            margin-bottom: 4rem;
            text-align: center;
            letter-spacing: 6px;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 30px rgba(255, 215, 0, 0.5), 0 0 60px rgba(255, 215, 0, 0.3); }
            to { text-shadow: 0 0 40px rgba(255, 215, 0, 0.8), 0 0 80px rgba(255, 215, 0, 0.5); }
        }

        .cities-container {
            display: flex;
            gap: 3rem;
            align-items: flex-start;
            justify-content: center;
            flex-wrap: wrap;
        }

        .city-card {
            background: linear-gradient(145deg, rgba(255,215,0,0.95), rgba(218,165,32,0.95));
            border-radius: 20px;
            padding: 2rem;
            width: 300px;
            border: 3px solid #B8860B;
            box-shadow: 0 15px 35px rgba(0,0,0,0.4), 0 0 25px rgba(255,215,0,0.3);
            position: relative;
            transition: all 0.4s ease;
            backdrop-filter: blur(10px);
        }

        .city-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.5), 0 0 40px rgba(255,215,0,0.5);
        }

        .city-card.silver {
            background: linear-gradient(145deg, rgba(192,192,192,0.95), rgba(169,169,169,0.95));
            border-color: #A9A9A9;
            box-shadow: 0 15px 35px rgba(0,0,0,0.4), 0 0 25px rgba(192,192,192,0.3);
        }

        .city-card.silver:hover {
            box-shadow: 0 25px 50px rgba(0,0,0,0.5), 0 0 40px rgba(192,192,192,0.5);
        }

        .city-card.bronze {
            background: linear-gradient(145deg, rgba(205,127,50,0.95), rgba(184,115,51,0.95));
            border-color: #CD7F32;
            box-shadow: 0 15px 35px rgba(0,0,0,0.4), 0 0 25px rgba(205,127,50,0.3);
        }

        .city-card.bronze:hover {
            box-shadow: 0 25px 50px rgba(0,0,0,0.5), 0 0 40px rgba(205,127,50,0.5);
        }

        .rank-badge {
            position: absolute;
            top: -20px;
            left: 25px;
            background: #B8860B;
            color: white;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Orbitron', monospace;
            font-weight: 900;
            font-size: 2rem;
            border: 4px solid white;
            box-shadow: 0 8px 20px rgba(0,0,0,0.4);
            z-index: 5;
        }

        .rank-badge.silver {
            background: #C0C0C0;
        }

        .rank-badge.bronze {
            background: #CD7F32;
        }

        .city-image {
            width: 100%;
            height: 150px;
            background-size: cover;
            background-position: center;
            border-radius: 15px;
            margin-bottom: 1.5rem;
            border: 3px solid rgba(255,255,255,0.4);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .sarasota-bg { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 150"><defs><linearGradient id="sky" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color:%2387CEEB"/><stop offset="100%" style="stop-color:%23E0F6FF"/></linearGradient></defs><rect width="300" height="150" fill="url(%23sky)"/><circle cx="250" cy="30" r="20" fill="%23FFD700"/><rect x="50" y="80" width="25" height="60" fill="%234682B4"/><rect x="80" y="70" width="30" height="70" fill="%234169E1"/><rect x="115" y="85" width="20" height="55" fill="%23483D8B"/><rect x="140" y="75" width="25" height="65" fill="%234682B4"/><ellipse cx="200" cy="120" rx="15" ry="5" fill="%2332CD32"/><ellipse cx="230" cy="125" rx="20" ry="8" fill="%2332CD32"/><rect x="0" y="130" width="300" height="20" fill="%234169E1"/></svg>'); }

        .sanfrancisco-bg { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 150"><defs><linearGradient id="sky2" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color:%23B0C4DE"/><stop offset="100%" style="stop-color:%23E6E6FA"/></linearGradient></defs><rect width="300" height="150" fill="url(%23sky2)"/><rect x="30" y="60" width="20" height="80" fill="%23696969"/><rect x="55" y="40" width="25" height="100" fill="%23808080"/><rect x="85" y="55" width="18" height="85" fill="%23A9A9A9"/><rect x="108" y="45" width="22" height="95" fill="%23696969"/><rect x="135" y="70" width="15" height="70" fill="%23778899"/><path d="M160 90 Q200 70 240 90 L240 140 L160 140 Z" fill="%23FF6347"/><rect x="0" y="130" width="300" height="20" fill="%234682B4"/></svg>'); }

        .mykolaiv-bg { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 150"><defs><linearGradient id="sky3" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color:%2387CEEB"/><stop offset="100%" style="stop-color:%23F0F8FF"/></linearGradient></defs><rect width="300" height="150" fill="url(%23sky3)"/><rect x="80" y="100" width="40" height="40" fill="%23DAA520"/><polygon points="80,100 100,70 120,100" fill="%23B22222"/><rect x="95" y="110" width="10" height="20" fill="%238B4513"/><rect x="140" y="90" width="35" height="50" fill="%23FFD700"/><polygon points="140,90 157.5,60 175,90" fill="%23DC143C"/><rect x="200" y="95" width="30" height="45" fill="%23F4A460"/><polygon points="200,95 215,75 230,95" fill="%23CD5C5C"/><ellipse cx="50" cy="125" rx="25" ry="10" fill="%2332CD32"/><ellipse cx="250" cy="130" rx="30" ry="12" fill="%2332CD32"/></svg>'); }

        .city-name {
            font-family: 'Playfair Display', serif;
            font-size: 2rem;
            font-weight: 700;
            color: #2C1810;
            margin-bottom: 0.5rem;
            text-align: center;
        }

        .city-location {
            font-family: 'Inter', sans-serif;
            font-size: 1rem;
            color: #5D4E37;
            text-align: center;
            margin-bottom: 1.5rem;
            font-weight: 600;
        }

        .stats-container {
            display: flex;
            justify-content: space-between;
            gap: 1rem;
        }

        .stat {
            text-align: center;
            flex: 1;
        }

        .stat-label {
            font-family: 'Inter', sans-serif;
            font-size: 0.9rem;
            color: #5D4E37;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .stat-value {
            font-family: 'Orbitron', monospace;
            font-size: 1.2rem;
            font-weight: 700;
            color: #2C1810;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.3rem;
        }

        .stat-icon {
            font-size: 1rem;
        }

        @media (max-width: 768px) {
            .title {
                font-size: 2.5rem;
                margin-bottom: 3rem;
                letter-spacing: 3px;
            }
            
            .cities-container {
                flex-direction: column;
                gap: 2.5rem;
                align-items: center;
            }
            
            .city-card {
                width: 90vw;
                max-width: 350px;
            }
        }

        @media (max-width: 480px) {
            .title {
                font-size: 2rem;
            }
            
            body {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">UPLAND TOP CITIES</h1>
        
        <div class="cities-container">
            <!-- Rank 2 - San Francisco -->
            <div class="city-card silver">
                <div class="rank-badge silver">2</div>
                <div class="city-image sanfrancisco-bg"></div>
                <div class="city-name">SAN-FRANCISCO</div>
                <div class="city-location">USA</div>
                <div class="stats-container">
                    <div class="stat">
                        <div class="stat-label">Residents</div>
                        <div class="stat-value">
                            <span class="stat-icon">👥</span>
                            4185
                        </div>
                    </div>
                    <div class="stat">
                        <div class="stat-label">Occupancy</div>
                        <div class="stat-value">
                            <span class="stat-icon">🏠</span>
                            94%
                        </div>
                    </div>
                    <div class="stat">
                        <div class="stat-label">Dens</div>
                        <div class="stat-value">94%</div>
                    </div>
                </div>
            </div>

            <!-- Rank 1 - Sarasota (Center/Main) -->
            <div class="city-card">
                <div class="rank-badge">1</div>
                <div class="city-image sarasota-bg"></div>
                <div class="city-name">SARASOTA</div>
                <div class="city-location">Florida, USA</div>
                <div class="stats-container">
                    <div class="stat">
                        <div class="stat-label">Residents</div>
                        <div class="stat-value">
                            <span class="stat-icon">👥</span>
                            4185
                        </div>
                    </div>
                    <div class="stat">
                        <div class="stat-label">Occupancy</div>
                        <div class="stat-value">
                            <span class="stat-icon">🏠</span>
                            94%
                        </div>
                    </div>
                    <div class="stat">
                        <div class="stat-label">Dens</div>
                        <div class="stat-value">94%</div>
                    </div>
                </div>
            </div>

            <!-- Rank 3 - Mykolaiv -->
            <div class="city-card bronze">
                <div class="rank-badge bronze">3</div>
                <div class="city-image mykolaiv-bg"></div>
                <div class="city-name">MYKOLAIV</div>
                <div class="city-location">Ukraine</div>
                <div class="stats-container">
                    <div class="stat">
                        <div class="stat-label">Residents</div>
                        <div class="stat-value">
                            <span class="stat-icon">👥</span>
                            4185
                        </div>
                    </div>
                    <div class="stat">
                        <div class="stat-label">Occupancy</div>
                        <div class="stat-value">
                            <span class="stat-icon">🏠</span>
                            94%
                        </div>
                    </div>
                    <div class="stat">
                        <div class="stat-label">Dens</div>
                        <div class="stat-value">94%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>